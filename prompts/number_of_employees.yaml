- name: find_information
  prompt: |
    From the following company description written in markdown, extract the following structured information:

    1. `num_employees` (int or null):
      - Total number of employees.
      - Accept only clearly stated numbers.
      - Include numbers described as employees, team members, workforce, staff, personnel, professionals, experts, etc.  
      - If range, take average (e.g., 50-100 → 75).  
      - If “more than 100”, return 100.  
      - Return null if unsure.

    2. `employees_quote` (str):
      - **Exact quote** from the markdown that contains the number from `num_employees`.
      - The quote must be a full, standalone sentence or two that clearly states the number.  
      - Do not return a partial sentence or paraphrase.  
      - If no such quote is found, or if "num_employees" is null, return "Unknown".

    Respond with a JSON object like:

    {{
      "num_employees": ...,   // int. Number of total employees. Null if not found.
      "employees_quote": "...",   // str. Full sentence with the employee count. '' if not found.
    }}
    
    But don't preface it with ```json.

    <markdown input>
    {markdown}
    </markdown input>

- name: validate_results
  prompt: |
    You are verifying whether an employee count extracted from a company's website is correctly supported by the quote provided.

    Check the following:
    - Does the quote clearly support the number?
    - Does it refer to the total number of employees? (e.g., using terms like employees, workforce, staff, personnel, team members etc)

    If the quote is vague or doesn't clearly refer to total employees, mark it as employees_valid = "no".

    Respond with a JSON object like:
    {{
      "employees_valid": "yes" or "no",   //does the quote support the given num_employees?
      "correction_reason": "..."   //(optional explanation if the answer is "no")
    }}

    But don't preface it with ```json.

    Input:
    - num_employees: {find_information[num_employees]}
    - employees_quote: "{find_information[employees_quote]}"