
from typing import List, Dict, Type, TypedDict, Any
import json
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
import os


def make_node(llm, prompt, state_key):
    def node(state: Dict[str, Any]) -> Dict[str, str]:
        # print(f"Node {state_key} received state with keys: {list(state.keys())}")

        try:
            filled = prompt.format(**state)
        except KeyError as e:
            print(f"Missing key in state: {e}")
            print(f"Available keys: {list(state.keys())}")
            raise

        # Call the LLM using the original message types
        messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content=filled)
        ]
        try:
            response = llm.invoke(messages).content
        except Exception as e:
            raise Exception(e)

        try:
            parsed = json.loads(response)
            response = parsed
        except Exception:
            pass

        # print(f"RESPONSE: {response}")

        return {state_key: response}

    return node

class LangGraphUtils:
    def __init__(self, model="gpt-4o-mini"):
        if model.lower().startswith("gpt"):
            self.llm = ChatOpenAI(model=model, temperature=0.0, api_key=os.getenv("OPEN_AI_API_KEY"))
        else:
            print("Using Ollama")
            self.llm = ChatOpenAI(model=model, base_url="https://ollama.synergy-impact.de/v1", api_key="ollama-test-123456789" )

    def build_graph(self, prompts, workflow):
        """Build a LangGraph from a list of prompts"""

        # Create a state graph with our dynamic state type
        sg = StateGraph(workflow)

        # Create a node for each prompt
        for step in prompts:
            step_name = step['name']
            prompt_template = step['prompt']
            node_name = f"{step_name}_node"

            sg.add_node(node_name, make_node(self.llm, prompt_template, step_name))

        # Create edges between nodes
        for i in range(len(prompts) - 1):
            current_node = f"{prompts[i]['name']}_node"
            next_node = f"{prompts[i + 1]['name']}_node"
            sg.add_edge(current_node, next_node)

        # Set entry point and final edge
        first_node = f"{prompts[0]['name']}_node"
        last_node = f"{prompts[-1]['name']}_node"
        sg.add_edge(last_node, END)
        sg.set_entry_point(first_node)

        # Compile the graph
        return sg.compile()


