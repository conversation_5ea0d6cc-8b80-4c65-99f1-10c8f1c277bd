from src import utils
import src.LangGraphUtils as <PERSON><PERSON><PERSON><PERSON><PERSON>tils
from langsmith.run_helpers import get_current_run_tree
import langsmith as ls


from dotenv import load_dotenv
import os

load_dotenv()

with ls.trace("prompts-pipeline") as rt:
    print("starting")
    prompts = utils.load_prompt_sequence("/Users/<USER>/Documents/GitHub/prompts-library/prompts/number_of_employees.yaml")
    markdown = utils.load_markdown("/Users/<USER>/Documents/GitHub/prompts-library/markdowns/aktax.de.md") #utils.load_markdown_db("dornbach.de", "wget_markdown")

    workflow = utils.create_workflow_state(prompts)
    lg = LangGraphUtils.LangGraphUtils(model="llama3:latest")
    graph = lg.build_graph(prompts, workflow)

    print("Running graph")
    initial_state = {"markdown": markdown}
    state_stream = graph.stream(initial_state)

    print("updates")
    for update in state_stream:
        print(update)

    rt.tags.extend(["another-tag"])


#
# # ─── naïve char-based chunking ─────────────────────────────────────────────────
# CHUNK_SIZE   = 1_250_000    # keep well below model limit
# OVERLAP      = 10_000    # optional; set 0 for none
# step         = CHUNK_SIZE - OVERLAP
#
# chunks = [markdown[i : i + CHUNK_SIZE] for i in range(0, len(markdown), step)]
#
# # ─── run graph chunk-by-chunk ─────────────────────────────────────────────────
# aggregate = {}
#
# for n, ck in enumerate(chunks, 1):
#     print(f"chunk {n}/{len(chunks)}")
#     seed_state = {"markdown": ck, **aggregate}
#     try:
#         for delta in graph.stream(seed_state):
#             print(delta)
#             aggregate.update(delta)
#     except Exception as e:
#         print(e)
