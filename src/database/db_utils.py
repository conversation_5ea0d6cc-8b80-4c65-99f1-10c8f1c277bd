import os
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Text, TIMESTAMP, func, UniqueConstraint
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from pathlib import Path
import src.utils as utils
import json
from sqlalchemy import text

# Load environment variables
load_dotenv(Path(__file__).resolve().parent.parent / ".env")


def create_manual_strip_profiles_table():
    """Create the manual_strip_profiles table if it doesn't exist"""
    engine = utils.get_engine()
    metadata = MetaData()

    # Define the table schema
    manual_strip_profiles = Table(
        'manual_strip_profiles',
        metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('cib_id', String(100), nullable=False),
        Column('property', String(100), nullable=False),
        Column('value', Text, nullable=True),
        Column('updated_at', TIMESTAMP, nullable=False, server_default=func.now()),
        UniqueConstraint('cib_id', 'property', 'updated_at', name='uq_cib_property_updated')
        # source_file omitted as per your note
        # Unique per field update per cib_id per time
        # Note: If you want latest overwrite logic, add unique (cib_id, property) instead
    )

    # Create table if it doesn't exist
    metadata.create_all(engine)

    # Create index manually to include DESC
    with engine.connect() as conn:
        conn.execute(text("""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes WHERE indexname = 'idx_cib_property_updated_desc'
                ) THEN
                    CREATE INDEX idx_cib_property_updated_desc
                    ON manual_strip_profiles (cib_id, property, updated_at DESC);
                END IF;
            END
            $$;
        """))
        conn.commit()

    return manual_strip_profiles

def get_manual_strip_profiles_table():
    """Get the manual_strip_profiles table object"""
    engine = utils.get_engine()
    metadata = MetaData()

    # Try to reflect existing table, create if doesn't exist
    try:
        metadata.reflect(bind=engine)
        if 'manual_strip_profiles' in metadata.tables:
            return metadata.tables['manual_strip_profiles']
    except Exception:
        pass

    # Create table if reflection failed or table doesn't exist
    return create_manual_strip_profiles_table()


from sqlalchemy import text
import src.utils as utils



def create_materialized_view_expanded_strip_profiles():
    def sanitize_column_name(key: str) -> str:
        return key.strip().replace('.', '_').replace('-', '_').replace(' ', '_')

    # Define your static list of properties
    # json_keys = [
    #     "company_name", "product_summary", "product_summary_noverb", "product_summary_source_complete",
    #     "product_summary_source_text", "product_summary_source_date", "product_summary_source_category",
    #     "NT", "NT_source_complete", "payroll_focus", "payroll_focus_source_complete", "payroll_focus_source_text",
    #     "payroll_focus_source_date", "payroll_focus_source_category", "Stb", "Stb_source_complete", "WP",
    #     "WP_source_complete", "RA", "RA_source_complete", "UB", "UB_source_complete", "Other",
    #     "Other_source_complete", "client_summary", "client_summary_source_complete", "client_focus",
    #     "client_focus_source_complete", "client_summary_source_text", "client_summary_source_date",
    #     "client_summary_source_category", "firm_years_summary", "firm_years_summary_source_complete",
    #     "firm_years_summary_source_text", "firm_years_summary_source_date", "firm_years_summary_source_category",
    #     "locations_text", "locations_text_source_complete", "locations_text_source_date",
    #     "locations_text_sorce_category", "hq", "hq_source_complete", "hq_source_text", "hq_source_date",
    #     "hq_source_category", "AUM", "aum_source_complete", "AUM_source_date", "AUM_source_category",
    #     "AUM_detail", "aum_detail_source_complete", "AUM_detail_source_date", "AUM_detail_source_category",
    #     "foundingyear_HQ_combined", "foundingyear_HQ_combined_source_complete",
    #     "foundingyear_HQ_combined_source_date", "foundingyear_HQ_combined_source_category",
    #     "total_employees", "total_employees_strip", "total_employees_source", "total_employees_source_complete",
    #     "total_employees_cagr", "total_employees_cagr_source_complete", "tax_professionals_registry",
    #     "tax_professionals_registry_source_complete", "total_professionals", "total_professionals_source_complete",
    #     "revenue", "revenue_strip", "revenue_source_complete", "revenue_cagr", "revenue_cagr_source_complete",
    #     "operating_profit", "operating_profit_strip", "operating_profit_source_complete", "operating_profit_cagr",
    #     "operating_profit_cagr_source_complete", "ownership_type", "ownership_type_short",
    #     "ownership_type_source_complete", "ownership_type_short_source_complete", "ownership_detail",
    #     "ownership_detail_source_complete", "url", "total_employees_estimated",
    #     "total_employees_estimated_source_complete", "revenues_estimated", "revenues_estimated_source_complete",
    #     "law_focus", "law_focus_source_complete", "total_front_end_employees",
    #     "total_front_end_employees_source_complete", "SV1", "SV1_source_complete", "SV2", "SV2_source_complete",
    #     "SV3", "SV3_source_complete", "SV4", "SV4_source_complete"
    # ]

    engine = utils.get_engine()

    # Dynamically get all distinct property values from the table
    with engine.connect() as conn:
        result = conn.execute(text("SELECT DISTINCT property FROM manual_strip_profiles ORDER BY property"))
        json_keys = [row[0] for row in result]

        print(f"Found {len(json_keys)} distinct properties in the database")

    # Remove duplicates
    #json_keys = list(set(json_keys))

    # Sanitize only invalid characters, preserve case
    sanitized_key_map = {sanitize_column_name(k): k for k in json_keys}

    # Use original keys in the CASE WHEN and quote them for PostgreSQL to preserve case
    # Exclude values that are "unknown" (case insensitive)
    column_exprs = [
        f"MAX(CASE WHEN property = '{original}' AND LOWER(value) != 'unknown' THEN value END) AS \"{original}\""
        for original in json_keys
    ]
    select_columns = ",\n    ".join(column_exprs)

    engine = utils.get_engine()
    with engine.connect() as conn:
        print("Dropping materialized view if exists...")

        conn.execute(text("DROP MATERIALIZED VIEW IF EXISTS manual_strip_profiles_latest_expanded CASCADE;"))

        print("Creating materialized view...")

        view_sql = f"""
        CREATE MATERIALIZED VIEW manual_strip_profiles_latest_expanded AS
        SELECT
            cib_id,
            {select_columns}
        FROM (
            SELECT DISTINCT ON (cib_id, property) *
            FROM manual_strip_profiles
            ORDER BY cib_id, property, updated_at DESC
        ) latest
        GROUP BY cib_id;
        """

        conn.execute(text(view_sql))

        conn.commit()

        print("✅ Materialized view created successfully.")

