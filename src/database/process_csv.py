import sys
import json
import argparse
import pandas as pd
from pathlib import Path
import numpy as np
import src.utils as utils
from sqlalchemy import text

from src.database.db_utils import get_manual_strip_profiles_table, create_materialized_view_expanded_strip_profiles

def process_csv(csv_file_path: str, separator: str = ';'):
    df = pd.read_csv(csv_file_path, sep=separator)

    # If cib_id doesn't exist, create it from url (but keep url)
    if 'cib_id' not in df.columns:
        if 'url' not in df.columns:
            raise ValueError("CSV must contain either 'cib_id' or 'url' column")
        df['cib_id'] = df['url'].astype(str)

    # to ignore duplicates
    df.set_index('cib_id', inplace=True)
    df = df[~df.index.duplicated(keep='first')]
    df.reset_index(inplace=True)
    print(f"Read CSV with {len(df)} rows and columns: {list(df.columns)}")


    # Normalize: explode each row into (cib_id, property, value)
    records = []

    for _, row in df.iterrows():
        cib_id = row["cib_id"]
        for col, val in row.items():
            if col == "cib_id" or pd.isna(val):
                continue
            records.append({
                "cib_id": str(cib_id),
                "property": str(col).strip(),
                "value": str(val).strip()
            })

    print(f"Prepared {len(records)} normalized property records for insertion")

    engine = utils.get_engine()
    table = get_manual_strip_profiles_table()  # Ensure table exists

    # Insert records
    with engine.begin() as conn:
        conn.execute(table.insert(), records)

    print(f"✅ Successfully inserted {len(records)} property values from {csv_file_path}")

def main():
    """Main function"""
    print("Starting")
    parser = argparse.ArgumentParser(description='Process CSV file and insert into manual_strip_profiles table')
    parser.add_argument('csv_file', help='Path to the CSV file')
    parser.add_argument('separator', help='Column separator')

    args = parser.parse_args()

    print(f"Will process {args.csv_file}")
    process_csv(args.csv_file, args.separator)
    print("Done, will create materialized view now.")
    create_materialized_view_expanded_strip_profiles()

if __name__ == "__main__":
    main()



#process_csv("/Users/<USER>/Desktop/last_strip_profiles.csv")
#create_materialized_view_expanded_strip_profiles()